<template>
  <el-dialog
    v-model="dialogVisible"
    title="专家详情"
    width="85%"
    :before-close="handleClose"
    class="expert-detail-dialog"
    top="3vh"
    :close-on-click-modal="false"
  >
    <div v-if="expert" class="expert-detail-content">
      <!-- 专家基本信息卡片 -->
      <el-card class="expert-header-card" shadow="hover">
        <div class="expert-header">
          <div class="expert-avatar-section">
            <el-avatar
              :size="160"
              :src="expert.avatar || defaultAvatar"
              class="expert-avatar-large"
              fit="cover"
            >
              <el-icon><User /></el-icon>
            </el-avatar>
          </div>
          <div class="expert-basic-info">
            <div class="expert-name-title">
              <h2 class="expert-name">
                <el-icon class="name-icon"><Star /></el-icon>
                {{ expert.name }}
              </h2>
              <el-tag type="success" size="large" class="expert-title">
                {{ expert.work }}
              </el-tag>
            </div>

            <div class="expert-tags-section">
              <div class="tags-label">
                <el-icon><Collection /></el-icon>
                <span>专业标签</span>
              </div>
              <div class="tags-container">
                <el-tag
                  v-for="tag in expert.tags"
                  :key="tag"
                  type="primary"
                  effect="light"
                  class="expert-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>

            <div class="expert-contact-info">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="contact-item">
                    <el-icon class="contact-icon"><Briefcase /></el-icon>
                    <span class="contact-label">职称：</span>
                    <span class="contact-value">{{ expert.work }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="contact-item">
                    <el-icon class="contact-icon"><Phone /></el-icon>
                    <span class="contact-label">电话：</span>
                    <span class="contact-value">{{ expert.phone }}</span>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="contact-item">
                    <el-icon class="contact-icon"><OfficeBuilding /></el-icon>
                    <span class="contact-label">工作单位：</span>
                    <span class="contact-value">{{ expert.department }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 详细介绍 -->
      <el-card class="expert-section-card" shadow="hover">
        <template #header>
          <div class="section-header">
            <el-icon class="section-icon"><Document /></el-icon>
            <span class="section-title">详细介绍</span>
          </div>
        </template>
        <div class="section-content">
          <el-text class="expert-introduction" size="large">
            {{ expert.introduction }}
          </el-text>
        </div>
      </el-card>

      <!-- 专业信息 -->
      <el-card class="expert-section-card" shadow="hover">
        <template #header>
          <div class="section-header">
            <el-icon class="section-icon"><User /></el-icon>
            <span class="section-title">专业信息</span>
          </div>
        </template>
        <div class="section-content">
          <el-row :gutter="24" class="info-grid">
            <el-col :span="12">
              <div class="info-item">
                <el-icon class="info-icon"><School /></el-icon>
                <span class="info-label">学历：</span>
                <el-tag type="info" effect="light">{{ expert.education }}</el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <el-icon class="info-icon"><Search /></el-icon>
                <span class="info-label">研究方向：</span>
                <el-tag type="warning" effect="light">{{ expert.researchDirection }}</el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <el-icon class="info-icon"><Timer /></el-icon>
                <span class="info-label">从业经验：</span>
                <el-tag type="success" effect="light">{{ expert.experience }}</el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <el-icon class="info-icon"><Message /></el-icon>
                <span class="info-label">邮箱：</span>
                <el-link type="primary" :href="`mailto:${expert.email}`">{{
                  expert.email
                }}</el-link>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 专业特长 -->
      <el-card
        v-if="expert.specialties && expert.specialties.length"
        class="expert-section-card"
        shadow="hover"
      >
        <template #header>
          <div class="section-header">
            <el-icon class="section-icon"><Medal /></el-icon>
            <span class="section-title">专业特长</span>
          </div>
        </template>
        <div class="section-content">
          <div class="specialties-list">
            <el-tag
              v-for="specialty in expert.specialties"
              :key="specialty"
              type="primary"
              size="large"
              effect="dark"
              class="specialty-tag"
            >
              <el-icon><Star /></el-icon>
              {{ specialty }}
            </el-tag>
          </div>
        </div>
      </el-card>

      <!-- 主要成就 -->
      <el-card
        v-if="expert.achievements && expert.achievements.length"
        class="expert-section-card"
        shadow="hover"
      >
        <template #header>
          <div class="section-header">
            <el-icon class="section-icon"><Trophy /></el-icon>
            <span class="section-title">主要成就</span>
          </div>
        </template>
        <div class="section-content">
          <div class="achievements-list">
            <el-timeline>
              <el-timeline-item
                v-for="(achievement, index) in expert.achievements"
                :key="achievement"
                :icon="Trophy"
                type="primary"
                size="large"
              >
                <el-text class="achievement-item">{{ achievement }}</el-text>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </el-card>

      <!-- 主要项目 -->
      <el-card
        v-if="expert.projects && expert.projects.length"
        class="expert-section-card"
        shadow="hover"
      >
        <template #header>
          <div class="section-header">
            <el-icon class="section-icon"><Folder /></el-icon>
            <span class="section-title">主要项目</span>
          </div>
        </template>
        <div class="section-content">
          <div class="projects-list">
            <el-row :gutter="16">
              <el-col
                v-for="project in expert.projects"
                :key="project.name"
                :span="12"
                class="project-col"
              >
                <el-card class="project-item-card" shadow="hover">
                  <div class="project-header">
                    <div class="project-name-section">
                      <el-icon class="project-icon"><FolderOpened /></el-icon>
                      <h4 class="project-name">{{ project.name }}</h4>
                    </div>
                    <el-tag type="info" class="project-year">{{ project.year }}</el-tag>
                  </div>
                  <el-divider />
                  <div class="project-details">
                    <div class="project-detail-item">
                      <el-icon class="detail-icon"><Money /></el-icon>
                      <span class="detail-label">资助来源：</span>
                      <span class="detail-value">{{ project.funding }}</span>
                    </div>
                    <div class="project-detail-item">
                      <el-icon class="detail-icon"><UserFilled /></el-icon>
                      <span class="detail-label">担任角色：</span>
                      <el-tag type="success" size="small">{{ project.role }}</el-tag>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-card>

      <!-- 主要论文 -->
      <el-card
        v-if="expert.publications && expert.publications.length"
        class="expert-section-card"
        shadow="hover"
      >
        <template #header>
          <div class="section-header">
            <el-icon class="section-icon"><Reading /></el-icon>
            <span class="section-title">主要论文</span>
          </div>
        </template>
        <div class="section-content">
          <div class="publications-list">
            <el-collapse accordion>
              <el-collapse-item
                v-for="(publication, index) in expert.publications"
                :key="publication.title"
                :name="index"
                class="publication-item"
              >
                <template #title>
                  <div class="publication-title-section">
                    <el-icon class="publication-icon"><Document /></el-icon>
                    <span class="publication-title">{{ publication.title }}</span>
                  </div>
                </template>
                <div class="publication-details">
                  <el-row :gutter="16">
                    <el-col :span="8">
                      <div class="publication-detail">
                        <el-icon class="detail-icon"><Notebook /></el-icon>
                        <span class="detail-label">期刊：</span>
                        <el-tag type="primary">{{ publication.journal }}</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="publication-detail">
                        <el-icon class="detail-icon"><Calendar /></el-icon>
                        <span class="detail-label">年份：</span>
                        <el-tag type="info">{{ publication.year }}</el-tag>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="publication-detail">
                        <el-icon class="detail-icon"><TrendCharts /></el-icon>
                        <span class="detail-label">影响因子：</span>
                        <el-tag type="warning">{{ publication.impact }}</el-tag>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button size="large" @click="handleClose">
          <el-icon><Close /></el-icon>
          关闭
        </el-button>
        <el-button type="primary" size="large" @click="handleContact">
          <el-icon><ChatDotRound /></el-icon>
          立即咨询
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import {
  User,
  Star,
  Collection,
  Briefcase,
  Phone,
  OfficeBuilding,
  Document,
  School,
  Search,
  Timer,
  Message,
  Medal,
  Trophy,
  Folder,
  FolderOpened,
  Money,
  UserFilled,
  Reading,
  Notebook,
  Calendar,
  TrendCharts,
  Close,
  ChatDotRound
} from '@element-plus/icons-vue'

export default {
  name: 'ExpertDetail',
  components: {
    User,
    Star,
    Collection,
    Briefcase,
    Phone,
    OfficeBuilding,
    Document,
    School,
    Search,
    Timer,
    Message,
    Medal,
    Trophy,
    Folder,
    FolderOpened,
    Money,
    UserFilled,
    Reading,
    Notebook,
    Calendar,
    TrendCharts,
    Close,
    ChatDotRound
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    expert: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      defaultAvatar: '/src/assets/imgs/avatar.jpg'
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        if (!value) {
          this.handleClose()
        }
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleContact() {
      if (this.expert) {
        this.$message.success(`正在联系 ${this.expert.name}`)
        // 这里可以添加实际的联系逻辑
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.expert-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: 80vh;
    overflow-y: auto;
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;

    .el-dialog__title {
      color: white;
      font-size: 20px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 20px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }
}

.expert-detail-content {
  .expert-header-card {
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;

    :deep(.el-card__body) {
      padding: 30px;
    }
  }

  .expert-section-card {
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    :deep(.el-card__header) {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      color: white;
      padding: 15px 20px;

      .section-header {
        display: flex;
        align-items: center;
        gap: 8px;

        .section-icon {
          font-size: 18px;
        }

        .section-title {
          font-size: 16px;
          font-weight: 600;
          margin: 0;
        }
      }
    }

    :deep(.el-card__body) {
      padding: 25px;
    }
  }
}

.expert-header {
  display: flex;
  gap: 30px;
  align-items: flex-start;

  .expert-avatar-section {
    flex-shrink: 0;

    .expert-avatar-large {
      border-radius: 50%;
      overflow: hidden;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .expert-basic-info {
    flex: 1;

    .expert-name-title {
      margin-bottom: 20px;

      .expert-name {
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 0 0 10px 0;
        font-size: 28px;
        font-weight: 700;
        color: #2c3e50;

        .name-icon {
          color: #f39c12;
          font-size: 24px;
        }
      }

      .expert-title {
        font-size: 16px;
      }
    }

    .expert-tags-section {
      margin-bottom: 25px;

      .tags-label {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 600;
        color: #34495e;
      }

      .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .expert-tag {
          font-size: 14px;
          padding: 8px 16px;
          border-radius: 20px;
        }
      }
    }

    .expert-contact-info {
      .contact-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 0;
        border-bottom: 1px solid #ecf0f1;

        &:last-child {
          border-bottom: none;
        }

        .contact-icon {
          color: #3498db;
          font-size: 16px;
        }

        .contact-label {
          font-weight: 600;
          color: #2c3e50;
          min-width: 60px;
        }

        .contact-value {
          color: #7f8c8d;
        }
      }
    }
  }
}

.section-content {
  .expert-introduction {
    line-height: 1.8;
    color: #2c3e50;
    font-size: 16px;
    text-align: justify;
  }

  .info-grid {
    .info-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 15px 0;
      border-bottom: 1px solid #ecf0f1;

      &:last-child {
        border-bottom: none;
      }

      .info-icon {
        color: #3498db;
        font-size: 18px;
      }

      .info-label {
        font-weight: 600;
        color: #2c3e50;
        min-width: 80px;
      }
    }
  }

  .specialties-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;

    .specialty-tag {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 12px 20px;
      border-radius: 25px;
      font-size: 14px;
      font-weight: 600;
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  .achievements-list {
    :deep(.el-timeline) {
      padding-left: 0;

      .el-timeline-item__wrapper {
        padding-left: 35px;

        .el-timeline-item__content {
          .achievement-item {
            font-size: 15px;
            line-height: 1.6;
            color: #2c3e50;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
          }
        }
      }
    }
  }

  .projects-list {
    .project-col {
      margin-bottom: 20px;
    }

    .project-item-card {
      height: 100%;
      border-radius: 10px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .project-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;

        .project-name-section {
          display: flex;
          align-items: center;
          gap: 10px;
          flex: 1;

          .project-icon {
            color: #e67e22;
            font-size: 18px;
          }

          .project-name {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.4;
          }
        }

        .project-year {
          flex-shrink: 0;
        }
      }

      .project-details {
        .project-detail-item {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .detail-icon {
            color: #27ae60;
            font-size: 16px;
          }

          .detail-label {
            font-weight: 600;
            color: #34495e;
            min-width: 80px;
          }

          .detail-value {
            color: #7f8c8d;
          }
        }
      }
    }
  }

  .publications-list {
    :deep(.el-collapse) {
      border: none;

      .el-collapse-item {
        margin-bottom: 15px;
        border: 1px solid #e1e8ed;
        border-radius: 10px;
        overflow: hidden;

        &:last-child {
          margin-bottom: 0;
        }

        .el-collapse-item__header {
          background: #f8f9fa;
          padding: 20px;
          border: none;

          &:hover {
            background: #e9ecef;
          }

          .publication-title-section {
            display: flex;
            align-items: center;
            gap: 12px;

            .publication-icon {
              color: #8e44ad;
              font-size: 18px;
            }

            .publication-title {
              font-size: 16px;
              font-weight: 600;
              color: #2c3e50;
              line-height: 1.4;
            }
          }
        }

        .el-collapse-item__content {
          padding: 0;

          .publication-details {
            padding: 20px;

            .publication-detail {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 10px 0;

              .detail-icon {
                color: #9b59b6;
                font-size: 16px;
              }

              .detail-label {
                font-weight: 600;
                color: #2c3e50;
                min-width: 60px;
              }
            }
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 20px 0;

  .el-button {
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-2px);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .expert-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;

    .expert-avatar-section {
      align-self: center;
    }
  }

  .expert-contact-info {
    :deep(.el-row) {
      .el-col {
        margin-bottom: 10px;
      }
    }
  }

  .info-grid {
    :deep(.el-row) {
      .el-col {
        margin-bottom: 15px;
      }
    }
  }

  .projects-list {
    .project-col {
      :deep(.el-col) {
        width: 100% !important;
      }
    }
  }
}
</style>
